package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

func main() {
	// 创建 MCP 客户端连接到 kateway MCP 服务器
	mcpClient, err := client.NewHTTPClient("http://localhost:8081")
	if err != nil {
		log.Fatalf("Failed to create MCP client: %v", err)
	}
	defer mcpClient.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 初始化连接
	if err := mcpClient.Initialize(ctx); err != nil {
		log.Fatalf("Failed to initialize MCP client: %v", err)
	}

	fmt.Println("Connected to Kateway MCP Server!")

	// 列出可用的工具
	fmt.Println("\n=== Available Tools ===")
	tools, err := mcpClient.ListTools(ctx)
	if err != nil {
		log.Fatalf("Failed to list tools: %v", err)
	}

	for _, tool := range tools {
		fmt.Printf("- %s: %s\n", tool.Name, tool.Description)
	}

	// 示例 1: 列出集群信息
	fmt.Println("\n=== Example 1: List Cluster Info ===")
	result, err := mcpClient.CallTool(ctx, mcp.CallToolRequest{
		Params: mcp.CallToolParams{
			Name: "list_cluster_info",
			Arguments: map[string]interface{}{
				"appID": "1234567890", // 替换为实际的 AppID
			},
		},
	})
	if err != nil {
		fmt.Printf("Error calling list_cluster_info: %v\n", err)
	} else {
		printResult("list_cluster_info", result)
	}

	// 示例 2: 获取集群信息
	fmt.Println("\n=== Example 2: Get Cluster Info ===")
	result, err = mcpClient.CallTool(ctx, mcp.CallToolRequest{
		Params: mcp.CallToolParams{
			Name: "get_cluster_info",
			Arguments: map[string]interface{}{
				"clusterID":    "cls-xxxxxxxx", // 替换为实际的集群 ID
				"refreshCache": false,
			},
		},
	})
	if err != nil {
		fmt.Printf("Error calling get_cluster_info: %v\n", err)
	} else {
		printResult("get_cluster_info", result)
	}

	// 示例 3: 获取任务报告
	fmt.Println("\n=== Example 3: Get Task Report ===")
	result, err = mcpClient.CallTool(ctx, mcp.CallToolRequest{
		Params: mcp.CallToolParams{
			Name:      "get_task_report",
			Arguments: map[string]interface{}{},
		},
	})
	if err != nil {
		fmt.Printf("Error calling get_task_report: %v\n", err)
	} else {
		printResult("get_task_report", result)
	}

	// 示例 4: 获取风险报告
	fmt.Println("\n=== Example 4: Get Risk Report ===")
	result, err = mcpClient.CallTool(ctx, mcp.CallToolRequest{
		Params: mcp.CallToolParams{
			Name: "get_risk_report",
			Arguments: map[string]interface{}{
				"state": "stock", // 可选: "stock" 或 "increment"
			},
		},
	})
	if err != nil {
		fmt.Printf("Error calling get_risk_report: %v\n", err)
	} else {
		printResult("get_risk_report", result)
	}

	fmt.Println("\n=== MCP Client Demo Completed ===")
}

func printResult(toolName string, result *mcp.CallToolResult) {
	if result.IsError {
		fmt.Printf("%s returned error: %s\n", toolName, getErrorMessage(result))
		return
	}

	fmt.Printf("%s result:\n", toolName)
	if len(result.Content) > 0 {
		for i, content := range result.Content {
			fmt.Printf("  Content %d: %s\n", i+1, formatContent(content))
		}
	} else {
		fmt.Printf("  No content returned\n")
	}
}

func getErrorMessage(result *mcp.CallToolResult) string {
	if len(result.Content) > 0 {
		if textContent, ok := result.Content[0].(mcp.TextContent); ok {
			return textContent.Text
		}
	}
	return "Unknown error"
}

func formatContent(content interface{}) string {
	switch c := content.(type) {
	case mcp.TextContent:
		// 尝试格式化 JSON
		var jsonData interface{}
		if err := json.Unmarshal([]byte(c.Text), &jsonData); err == nil {
			if formatted, err := json.MarshalIndent(jsonData, "    ", "  "); err == nil {
				return string(formatted)
			}
		}
		return c.Text
	case mcp.ImageContent:
		return fmt.Sprintf("Image: %s", c.Data)
	case mcp.ResourceContent:
		return fmt.Sprintf("Resource: %s", c.URI)
	default:
		return fmt.Sprintf("Unknown content type: %T", content)
	}
}

// checkServerStatus 检查服务器状态的辅助函数
func checkServerStatus() {
	fmt.Println("Checking Kateway MCP Server status...")
	
	resp, err := http.Get("http://localhost:80/mcp")
	if err != nil {
		fmt.Printf("Failed to check server status: %v\n", err)
		return
	}
	defer resp.Body.Close()

	var status map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&status); err != nil {
		fmt.Printf("Failed to decode server status: %v\n", err)
		return
	}

	fmt.Printf("Server Status: %+v\n", status)
}
