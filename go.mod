module git.woa.com/kateway/kateway-server

go 1.24.4

require (
	cloud.tencent.com/tke/master-operator v0.0.0-00010101000000-000000000000
	git.woa.com/kateway/pkg v0.0.0-20241017065122-356561998cbe
	git.woa.com/kmetis/kmetis v0.0.0-00010101000000-000000000000
	git.woa.com/misakazhou/loadbalancer-resource-api v1.0.1
	github.com/Masterminds/semver v1.5.0
	github.com/bitly/go-simplejson v0.5.1
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc
	github.com/fatih/structs v1.1.0
	github.com/go-logr/logr v1.4.2
	github.com/golang/glog v1.2.2
	github.com/gosuri/uitable v0.0.4
	github.com/hashicorp/go-multierror v1.1.1
	github.com/hashicorp/go-version v1.7.0
	github.com/jinzhu/configor v1.2.2
	github.com/jinzhu/gorm v1.9.16
	github.com/mitchellh/mapstructure v1.5.0
	github.com/opentracing/opentracing-go v1.2.0
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/pborman/uuid v1.2.1
	github.com/pkg/errors v0.9.1
	github.com/prometheus/client_golang v1.12.1
	github.com/samber/lo v1.47.0
	github.com/segmentio/ksuid v1.0.4
	github.com/sirupsen/logrus v1.9.3
	github.com/spf13/cast v1.7.1
	github.com/spf13/cobra v1.8.0
	github.com/spf13/pflag v1.0.5
	github.com/spf13/viper v1.18.2
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb v1.0.770
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.770
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sts v1.0.770
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag v1.0.770
	github.com/tidwall/gjson v1.17.3
	github.com/uber/jaeger-client-go v2.30.0+incompatible
	golang.org/x/crypto v0.28.0
	golang.org/x/exp v0.0.0-20240119083558-1b970713d09a // indirect
	google.golang.org/grpc v1.67.1
	gopkg.in/yaml.v3 v3.0.1
	gorm.io/gorm v1.25.10
	k8s.io/api v0.24.0
	k8s.io/apiextensions-apiserver v0.24.0
	k8s.io/apimachinery v0.24.0
	k8s.io/client-go v0.24.0
	k8s.io/klog/v2 v2.110.1
	k8s.io/utils v0.0.0-20220728103510-ee6ede2d64ed
	tkestack.io/tke v0.0.0-00010101000000-000000000000
)

require (
	git.code.oa.com/misakazhou/tke-nginx-ingress-api v0.0.0-00010101000000-000000000000
	git.code.oa.com/tkex-teg/lb-controlling-framework v1.4.3-tkex-teg
	git.woa.com/ianvs/ianvs-sdk v1.0.10
	git.woa.com/kateway/multi-cluster-ingress-api v1.0.0
	git.woa.com/kateway/multi-cluster-service-api v1.0.0
	git.woa.com/kateway/tke-service-config v1.0.17
	git.woa.com/zhiyan-monitor/sdk/go-sdk/v3 v3.3.7
	github.com/fatih/color v1.16.0
	github.com/gin-gonic/gin v1.10.0
	github.com/go-playground/assert/v2 v2.2.0
	github.com/google/uuid v1.6.0
	github.com/jinzhu/copier v0.4.0
	github.com/stretchr/testify v1.9.0
	github.com/swaggo/files v1.0.1
	github.com/swaggo/gin-swagger v1.6.0
	github.com/swaggo/swag v1.16.4
	golang.org/x/sync v0.8.0
	golang.org/x/text v0.19.0
	google.golang.org/protobuf v1.35.1
	gopkg.in/yaml.v2 v2.4.0
	gorm.io/plugin/soft_delete v1.2.1
	k8s.io/klog v1.0.0
	sigs.k8s.io/yaml v1.3.0
)

require (
	github.com/mark3labs/mcp-go v0.34.0
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
)

require (
	git.woa.com/polaris/polaris-go/v2 v2.4.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.0.9 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.0 // indirect
	git.woa.com/zhiyan-log/sdk-go/v2 v2.0.0-**************-ccc7867663c4
	git.woa.com/zhiyan-monitor/t-digest v0.0.6 // indirect
	github.com/BurntSushi/toml v1.2.0 // indirect
	github.com/KyleBanks/depth v1.2.1 // indirect
	github.com/benbjohnson/clock v1.3.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bytedance/sonic v1.11.8 // indirect
	github.com/bytedance/sonic/loader v0.1.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cloudwego/base64x v0.1.4 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/emicklei/go-restful/v3 v3.8.0 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.4 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-openapi/jsonpointer v0.19.6 // indirect
	github.com/go-openapi/jsonreference v0.20.2 // indirect
	github.com/go-openapi/spec v0.20.4 // indirect
	github.com/go-openapi/swag v0.22.3 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.22.0 // indirect
	github.com/go-sql-driver/mysql v1.7.1 // indirect
	github.com/goccy/go-json v0.10.3 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-migrate/migrate/v4 v4.17.0 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/gnostic v0.5.7-v3refs // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/gofuzz v1.2.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/imdario/mergo v0.3.12
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/cpuid/v2 v2.2.8 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.15 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.2-0.20181231171920-c182affec369 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/moby/spdystream v0.2.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_model v0.6.0 // indirect
	github.com/prometheus/common v0.32.1 // indirect
	github.com/prometheus/procfs v0.7.3 // indirect
	github.com/rivo/uniseg v0.2.0 // indirect
	github.com/sagikazarmark/locafero v0.4.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.9.0 // indirect
	go.uber.org/ratelimit v0.3.1
	go.uber.org/zap v1.21.0 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/net v0.30.0 // indirect
	golang.org/x/oauth2 v0.23.0 // indirect
	golang.org/x/sys v0.26.0 // indirect
	golang.org/x/term v0.25.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20241015192408-796eee8c2d53 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gorm.io/driver/mysql v1.5.2 // indirect
	gorm.io/plugin/opentracing v0.0.0-20211220013347-7d2b2af23560 // indirect
	k8s.io/kube-openapi v0.0.0-20220803162953-67bda5d908f1 // indirect
	sigs.k8s.io/json v0.0.0-20220713155537-f223a00ba0e2 // indirect
	sigs.k8s.io/structured-merge-diff/v4 v4.2.3 // indirect
)

replace (
	cloud.tencent.com/tke/master-operator => ./staging/cloud.tencent.com/tke/master-operator // v0.2.29
	git.code.oa.com/misakazhou/tke-nginx-ingress-api => git.woa.com/misakazhou/tke-nginx-ingress-api v1.1.1
	git.code.oa.com/tkex-teg/lb-controlling-framework => git.woa.com/tkex-teg/lb-controlling-framework v1.4.3-tkex-teg
	git.woa.com/kmetis/kmetis => ./staging/git.woa.com/kmetis/kmetis // v1.24.1
	github.com/Sirupsen/logrus => github.com/sirupsen/logrus v1.4.2
	google.golang.org/grpc => google.golang.org/grpc v1.69.2
	k8s.io/client-go => git.woa.com/kateway/client-go v0.24.1-0.20240115122041-5149aedf66a8
	tkestack.io/tke => ./staging/tkestack.io/tke // v1.9.1-0.20220415092613-137b5766f5f8
)
