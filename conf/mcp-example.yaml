# MCP 服务配置示例
# 将此配置添加到你的 kops.yaml 配置文件中

mcp:
  # 是否启用 MCP 服务，默认为 true
  enabled: true
  
  # MCP 服务端口，默认为 8081
  # MCP 服务将在此端口提供 HTTP 接口
  port: 8081
  
  # MCP 服务路径前缀，默认为 "/mcp"
  # 在主 HTTP 服务器（端口 80）上提供的 MCP 信息接口路径
  pathPrefix: "/mcp"

# 使用说明：
# 1. MCP 服务将在配置的端口（默认 8081）上启动
# 2. 可以通过 HTTP 方式连接到 MCP 服务器
# 3. 主 HTTP 服务器（端口 80）上的 /mcp 路径提供服务状态信息
# 4. MCP 客户端可以通过以下方式连接：
#    - HTTP: http://localhost:8081
#    - 信息接口: http://localhost:80/mcp

# 可用的 MCP 工具：
# - list_cluster_info: 列出指定 AppID 下的所有集群
# - get_cluster_info: 获取指定集群的详细信息
# - get_cluster_report: 获取指定集群的健康检查报告
# - get_cluster_risk_report: 获取指定集群的风险评估报告
# - get_clb_report: 获取指定 CLB 的健康检查报告
# - get_resource_report: 获取指定 Kubernetes 资源的健康检查报告
# - get_task_report: 获取健康检查任务报告
# - get_risk_report: 获取巡检风险报告
# - download_cluster_report: 下载集群扫描数据（CSV 格式）
# - download_risk_report: 下载风险数据（CSV 格式）
