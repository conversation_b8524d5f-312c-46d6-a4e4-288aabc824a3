package mcp

import (
	"context"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"

	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
)

// registerClusterTools registers all cluster-related MCP tools
func (s *MCPServer) registerClusterTools() {
	// List cluster info tool
	s.addTool(Tool{
		Definition: mcp.NewTool("list_cluster_info",
			mcp.WithDescription("List all clusters for a given AppID"),
			mcp.WithString("appID",
				mcp.Required(),
				mcp.Description("The AppID to list clusters for"),
			),
		),
		Handler: s.handleListClusterInfo,
	})

	// Get cluster info tool
	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_info",
			mcp.WithDescription("Get detailed information for a specific cluster"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID to get information for"),
			),
			mcp.WithBoolean("refreshCache",
				mcp.Description("Whether to force refresh the cache (default: false)"),
			),
		),
		Handler: s.handleGetClusterInfo,
	})

	// Get cluster report tool
	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_report",
			mcp.WithDescription("Get health check report for a specific cluster"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID to get report for"),
			),
			mcp.WithString("filter",
				mcp.Description("Filter type for the report (e.g., 'risk' for risk report)"),
			),
			mcp.WithBoolean("refreshCache",
				mcp.Description("Whether to force refresh the cache (default: false)"),
			),
		),
		Handler: s.handleGetClusterReport,
	})

	// Get cluster risk report tool
	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_risk_report",
			mcp.WithDescription("Get risk assessment report for a specific cluster"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID to get risk report for"),
			),
			mcp.WithBoolean("refreshCache",
				mcp.Description("Whether to force refresh the cache (default: false)"),
			),
		),
		Handler: s.handleGetClusterRiskReport,
	})
}

// handleListClusterInfo handles the list_cluster_info tool call
func (s *MCPServer) handleListClusterInfo(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate parameters
	appID, err := validateStringParam(request, "appID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Call the service
	clusterList, err := s.services.Inspection().ListCluster(ctx, appID)
	if err != nil {
		return formatError("Failed to list clusters", err), nil
	}

	// Format response
	response := map[string]interface{}{
		"totalCluster": len(clusterList),
		"state":        "Running",
		"clusters":     clusterList,
	}

	return formatSuccess(response), nil
}

// handleGetClusterInfo handles the get_cluster_info tool call
func (s *MCPServer) handleGetClusterInfo(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate parameters
	clusterID, err := validateStringParam(request, "clusterID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	refreshCache, err := validateOptionalBoolParam(request, "refreshCache")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get cluster reference
	cluster, err := s.services.Cluster().Get(ctx, strings.TrimSpace(clusterID))
	if err != nil {
		return formatError("Failed to get cluster", err), nil
	}

	// Get cluster info
	clusterInfo, err := s.services.Inspection().GetCluster(ctx, cluster, refreshCache)
	if err != nil {
		return formatError("Failed to get cluster info", err), nil
	}

	return formatSuccess(clusterInfo), nil
}

// handleGetClusterReport handles the get_cluster_report tool call
func (s *MCPServer) handleGetClusterReport(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate parameters
	clusterID, err := validateStringParam(request, "clusterID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	filter, err := validateOptionalStringParam(request, "filter")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	refreshCache, err := validateOptionalBoolParam(request, "refreshCache")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get cluster reference
	cluster, err := s.services.Cluster().Get(ctx, strings.TrimSpace(clusterID))
	if err != nil {
		return formatError("Failed to get cluster", err), nil
	}

	var report *model.ClusterReport

	// Get appropriate report based on filter
	if filter == "risk" {
		report, err = s.services.Inspection().GetClusterRiskReport(ctx, cluster, refreshCache)
	} else {
		report, err = s.services.Inspection().GetClusterReport(ctx, cluster, refreshCache)
	}

	if err != nil {
		return formatError("Failed to get cluster report", err), nil
	}

	return formatSuccess(report), nil
}

// handleGetClusterRiskReport handles the get_cluster_risk_report tool call
func (s *MCPServer) handleGetClusterRiskReport(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate parameters
	clusterID, err := validateStringParam(request, "clusterID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	refreshCache, err := validateOptionalBoolParam(request, "refreshCache")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get cluster reference
	cluster, err := s.services.Cluster().Get(ctx, strings.TrimSpace(clusterID))
	if err != nil {
		return formatError("Failed to get cluster", err), nil
	}

	// Get risk report
	report, err := s.services.Inspection().GetClusterRiskReport(ctx, cluster, refreshCache)
	if err != nil {
		return formatError("Failed to get cluster risk report", err), nil
	}

	return formatSuccess(report), nil
}
