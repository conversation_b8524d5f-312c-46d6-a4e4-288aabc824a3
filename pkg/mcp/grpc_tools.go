package mcp

import (
	"context"
	"strconv"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"

	"git.woa.com/kateway/kateway-server/pkg/service/clb"
)

// registerGRPCTools registers all gRPC-based MCP tools
func (s *MCPServer) registerGRPCTools() {
	// CLB 相关工具 - 直接通过 lbid 查询
	s.addTool(Tool{
		Definition: mcp.NewTool("get_clb_by_id",
			mcp.WithDescription("Get CLB (Cloud Load Balancer) information directly by LB ID, VIP, or domain"),
			mcp.WithString("query",
				mcp.Required(),
				mcp.Description("CLB query string: LB ID (lb-xxx), VIP address, or domain name"),
			),
		),
		Handler: s.handleGetCLBByID,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_clb_listeners",
			mcp.WithDescription("Get CLB listeners information directly by LB ID, VIP, or domain"),
			mcp.WithString("query",
				mcp.Required(),
				mcp.Description("CLB query string: LB ID (lb-xxx), VIP address, or domain name"),
			),
		),
		Handler: s.handleGetCLBListeners,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_clb_backends",
			mcp.WithDescription("Get CLB backend servers information directly by LB ID, VIP, or domain"),
			mcp.WithString("query",
				mcp.Required(),
				mcp.Description("CLB query string: LB ID (lb-xxx), VIP address, or domain name"),
			),
		),
		Handler: s.handleGetCLBBackends,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_clb_health",
			mcp.WithDescription("Get CLB health check status directly by LB ID, VIP, or domain"),
			mcp.WithString("query",
				mcp.Required(),
				mcp.Description("CLB query string: LB ID (lb-xxx), VIP address, or domain name"),
			),
		),
		Handler: s.handleGetCLBHealth,
	})

	// 用户和集群信息工具
	s.addTool(Tool{
		Definition: mcp.NewTool("get_user_info",
			mcp.WithDescription("Get user information by UIN or AppID"),
			mcp.WithString("query",
				mcp.Required(),
				mcp.Description("User query string: UIN or AppID"),
			),
		),
		Handler: s.handleGetUserInfo,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_by_id",
			mcp.WithDescription("Get cluster information directly by cluster ID"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID to get information for"),
			),
		),
		Handler: s.handleGetClusterByID,
	})

	// 镜像检查工具
	s.addTool(Tool{
		Definition: mcp.NewTool("check_image",
			mcp.WithDescription("Check if container image exists in specified regions"),
			mcp.WithString("name",
				mcp.Required(),
				mcp.Description("Component name (e.g., 'service', 'ingress')"),
			),
			mcp.WithString("version",
				mcp.Required(),
				mcp.Description("Image version to check"),
			),
			mcp.WithString("region",
				mcp.Description("Specific region to check (optional, checks all regions if not specified)"),
			),
		),
		Handler: s.handleCheckImage,
	})

	// 任务管理工具
	s.addTool(Tool{
		Definition: mcp.NewTool("list_tasks",
			mcp.WithDescription("List tasks with optional filters"),
			mcp.WithString("filters",
				mcp.Description("Filter string in format 'key1=value1,key2=value2' (optional)"),
			),
			mcp.WithNumber("limit",
				mcp.Description("Maximum number of tasks to return (optional)"),
			),
			mcp.WithNumber("offset",
				mcp.Description("Number of tasks to skip (optional)"),
			),
		),
		Handler: s.handleListTasks,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_task_by_id",
			mcp.WithDescription("Get detailed task information by task ID"),
			mcp.WithString("taskID",
				mcp.Required(),
				mcp.Description("The task ID to get information for"),
			),
		),
		Handler: s.handleGetTaskByID,
	})

	// Kubernetes 资源工具
	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_pods",
			mcp.WithDescription("Get pods information for a specific cluster"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID"),
			),
			mcp.WithString("controllerName",
				mcp.Required(),
				mcp.Description("Controller name ('service' or 'ingress')"),
			),
			mcp.WithString("user",
				mcp.Description("User name (optional if using token)"),
			),
			mcp.WithString("token",
				mcp.Description("Access token (optional)"),
			),
		),
		Handler: s.handleGetClusterPods,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_deployment",
			mcp.WithDescription("Get deployment information for a specific cluster"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID"),
			),
			mcp.WithString("controllerName",
				mcp.Required(),
				mcp.Description("Controller name ('service' or 'ingress')"),
			),
			mcp.WithString("user",
				mcp.Description("User name (optional if using token)"),
			),
			mcp.WithString("token",
				mcp.Description("Access token (optional)"),
			),
		),
		Handler: s.handleGetClusterDeployment,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_leader",
			mcp.WithDescription("Get leader pod information for a specific cluster"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID"),
			),
			mcp.WithString("controllerName",
				mcp.Required(),
				mcp.Description("Controller name ('service' or 'ingress')"),
			),
			mcp.WithString("user",
				mcp.Description("User name (optional if using token)"),
			),
			mcp.WithString("token",
				mcp.Description("Access token (optional)"),
			),
		),
		Handler: s.handleGetClusterLeader,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_config",
			mcp.WithDescription("Get configuration information for a specific cluster"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID"),
			),
			mcp.WithString("controllerName",
				mcp.Required(),
				mcp.Description("Controller name ('service' or 'ingress')"),
			),
			mcp.WithString("user",
				mcp.Description("User name (optional if using token)"),
			),
			mcp.WithString("token",
				mcp.Description("Access token (optional)"),
			),
		),
		Handler: s.handleGetClusterConfig,
	})

	s.addTool(Tool{
		Definition: mcp.NewTool("get_cluster_logs",
			mcp.WithDescription("Get logs from cluster leader pod"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID"),
			),
			mcp.WithString("controllerName",
				mcp.Required(),
				mcp.Description("Controller name ('service' or 'ingress')"),
			),
			mcp.WithString("sinceTime",
				mcp.Description("Get logs since this time (RFC3339 format, optional)"),
			),
			mcp.WithString("user",
				mcp.Description("User name (optional if using token)"),
			),
			mcp.WithString("token",
				mcp.Description("Access token (optional)"),
			),
		),
		Handler: s.handleGetClusterLogs,
	})
}

// CLB 相关处理函数

// handleGetCLBByID handles the get_clb_by_id tool call
func (s *MCPServer) handleGetCLBByID(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	query, err := validateStringParam(request, "query")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 调用 CLB 服务
	clbInfo, err := clb.Get(strings.TrimSpace(query))
	if err != nil {
		return formatError("Failed to get CLB information", err), nil
	}

	return formatSuccess(clbInfo), nil
}

// handleGetCLBListeners handles the get_clb_listeners tool call
func (s *MCPServer) handleGetCLBListeners(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	query, err := validateStringParam(request, "query")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 调用 CLB 服务
	listeners, err := clb.GetListeners(strings.TrimSpace(query))
	if err != nil {
		return formatError("Failed to get CLB listeners", err), nil
	}

	return formatSuccess(listeners), nil
}

// handleGetCLBBackends handles the get_clb_backends tool call
func (s *MCPServer) handleGetCLBBackends(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	query, err := validateStringParam(request, "query")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 调用 CLB 服务
	backends, err := clb.GetBackends(strings.TrimSpace(query))
	if err != nil {
		return formatError("Failed to get CLB backends", err), nil
	}

	return formatSuccess(backends), nil
}

// handleGetCLBHealth handles the get_clb_health tool call
func (s *MCPServer) handleGetCLBHealth(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	query, err := validateStringParam(request, "query")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 调用 CLB 服务
	health, err := clb.GetHealth(strings.TrimSpace(query))
	if err != nil {
		return formatError("Failed to get CLB health status", err), nil
	}

	return formatSuccess(health), nil
}

// 用户和集群信息处理函数

// handleGetUserInfo handles the get_user_info tool call
func (s *MCPServer) handleGetUserInfo(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	query, err := validateStringParam(request, "query")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 调用用户服务
	user, err := s.services.User().Get(ctx, strings.TrimSpace(query))
	if err != nil {
		return formatError("Failed to get user information", err), nil
	}

	return formatSuccess(user), nil
}

// handleGetClusterByID handles the get_cluster_by_id tool call
func (s *MCPServer) handleGetClusterByID(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	clusterID, err := validateStringParam(request, "clusterID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 调用集群服务
	cluster, err := s.services.Cluster().Get(ctx, strings.TrimSpace(clusterID))
	if err != nil {
		return formatError("Failed to get cluster information", err), nil
	}

	// 获取用户信息
	user, err := s.services.User().Get(ctx, strconv.FormatUint(cluster.Appid, 10))
	if err != nil {
		return formatError("Failed to get user information for cluster", err), nil
	}

	// 构建响应
	response := map[string]interface{}{
		"cluster": map[string]interface{}{
			"id":          cluster.ClusterID,
			"type":        cluster.Type,
			"name":        cluster.Name,
			"region":      cluster.Region,
			"metaCluster": cluster.MetaClusterID,
		},
		"user": user,
	}

	return formatSuccess(response), nil
}
