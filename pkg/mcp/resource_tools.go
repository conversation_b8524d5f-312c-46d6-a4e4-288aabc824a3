package mcp

import (
	"context"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
)

// registerResourceTools registers all resource-related MCP tools
func (s *MCPServer) registerResourceTools() {
	// Get resource report tool
	s.addTool(Tool{
		Definition: mcp.NewTool("get_resource_report",
			mcp.WithDescription("Get health check report for a specific Kubernetes resource"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID where the resource is located"),
			),
			mcp.WithString("resourceType",
				mcp.Required(),
				mcp.Description("The type of the resource (e.g., Service, Ingress, Deployment)"),
			),
			mcp.WithString("resourceNamespace",
				mcp.Required(),
				mcp.Description("The namespace of the resource"),
			),
			mcp.WithString("resourceName",
				mcp.Required(),
				mcp.Description("The name of the resource"),
			),
			mcp.With<PERSON>oolean("refreshCache",
				mcp.Description("Whether to force refresh the cache (default: false)"),
			),
		),
		Handler: s.handleGetResourceReport,
	})
}

// handleGetResourceReport handles the get_resource_report tool call
func (s *MCPServer) handleGetResourceReport(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate parameters
	clusterID, err := validateStringParam(request, "clusterID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	resourceType, err := validateStringParam(request, "resourceType")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	resourceNamespace, err := validateStringParam(request, "resourceNamespace")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	resourceName, err := validateStringParam(request, "resourceName")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	refreshCache, err := validateOptionalBoolParam(request, "refreshCache")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get cluster reference
	cluster, err := s.services.Cluster().Get(ctx, strings.TrimSpace(clusterID))
	if err != nil {
		return formatError("Failed to get cluster", err), nil
	}

	// Get resource report
	report, err := s.services.Inspection().GetResourceReport(ctx, cluster,
		strings.TrimSpace(resourceType),
		strings.TrimSpace(resourceNamespace),
		strings.TrimSpace(resourceName),
		refreshCache)
	if err != nil {
		return formatError("Failed to get resource report", err), nil
	}

	return formatSuccess(report), nil
}
