package mcp

import (
	"context"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
)

// registerTaskTools registers all task and risk-related MCP tools
func (s *MCPServer) registerTaskTools() {
	// Get task report tool
	s.addTool(Tool{
		Definition: mcp.NewTool("get_task_report",
			mcp.WithDescription("Get health check task report"),
			mcp.WithString("clusterID",
				mcp.Description("Filter tasks by cluster ID (optional)"),
			),
			mcp.WithString("state",
				mcp.Description("Filter tasks by state (optional)"),
			),
		),
		Handler: s.handleGetTaskReport,
	})

	// Get risk report tool
	s.addTool(Tool{
		Definition: mcp.NewTool("get_risk_report",
			mcp.WithDescription("Get inspection risk report"),
			mcp.WithString("state",
				mcp.Description("Filter risks by state: 'stock' for existing risks, 'increment' for new risks (optional)"),
				mcp.Enum("stock", "increment"),
			),
		),
		Handler: s.handleGetRiskReport,
	})
}

// handleGetTaskReport handles the get_task_report tool call
func (s *MCPServer) handleGetTaskReport(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate optional parameters
	clusterID, err := validateOptionalStringParam(request, "clusterID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	state, err := validateOptionalStringParam(request, "state")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get task report
	report, err := s.services.LegacyTask().GetTaskReport(ctx, strings.TrimSpace(clusterID), strings.TrimSpace(state))
	if err != nil {
		return formatError("Failed to get task report", err), nil
	}

	return formatSuccess(report), nil
}

// handleGetRiskReport handles the get_risk_report tool call
func (s *MCPServer) handleGetRiskReport(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate optional parameters
	state, err := validateOptionalStringParam(request, "state")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get risk report
	report, err := s.services.LegacyTask().GetRiskReport(ctx)
	if err != nil {
		return formatError("Failed to get risk report", err), nil
	}

	// Filter by state if specified
	if state == "stock" {
		report.Increment = nil
	} else if state == "increment" {
		report.Stock = nil
	}

	return formatSuccess(report), nil
}
