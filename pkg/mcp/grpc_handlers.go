package mcp

import (
	"context"
	"fmt"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/image"
	"git.woa.com/kateway/kateway-server/pkg/task"
	"git.woa.com/kateway/kateway-server/pkg/task/model"
)

// 镜像检查处理函数

// handleCheckImage handles the check_image tool call
func (s *MCPServer) handleCheckImage(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	name, err := validateStringParam(request, "name")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	version, err := validateStringParam(request, "version")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	region, err := validateOptionalStringParam(request, "region")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 获取地域列表
	var regions []string
	if region == "" {
		regions, err = s.services.Kateway().ListCCRRegion()
		if err != nil {
			return formatError("Failed to get regions", err), nil
		}
	} else {
		regions = []string{region}
	}

	// 构建镜像名称
	src := config.Get().GetImageName(strings.TrimSpace(name))

	// 检查镜像
	err = image.Check(src, []string{strings.TrimSpace(version)}, regions)

	response := map[string]interface{}{
		"image":   src,
		"version": version,
		"regions": regions,
		"status":  "success",
	}

	if err != nil {
		response["status"] = "failed"
		response["error"] = err.Error()
		return formatError("Image check failed", err), nil
	}

	return formatSuccess(response), nil
}

// 任务管理处理函数

// handleListTasks handles the list_tasks tool call
func (s *MCPServer) handleListTasks(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	filtersStr, err := validateOptionalStringParam(request, "filters")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	limit, err := validateOptionalIntParam(request, "limit")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	offset, err := validateOptionalIntParam(request, "offset")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 解析过滤器
	opts := []task.ListOption{task.WithSelectColumns("ID", "ParentID", "Type", "State", "CreatedAt")}

	if filtersStr != "" {
		filters, err := parseTaskFilters(filtersStr)
		if err != nil {
			return formatError("Invalid filters", err), nil
		}
		opts = append(opts, filters...)
	}

	if limit > 0 {
		opts = append(opts, task.WithLimit(limit))
	}
	if offset > 0 {
		opts = append(opts, task.WithOffset(offset))
	}

	// 调用任务服务
	svc := s.services.Task()
	tasks, total, err := svc.List(ctx, opts...)
	if err != nil {
		return formatError("Failed to list tasks", err), nil
	}

	response := map[string]interface{}{
		"total": total,
		"tasks": tasks,
	}

	return formatSuccess(response), nil
}

// handleGetTaskByID handles the get_task_by_id tool call
func (s *MCPServer) handleGetTaskByID(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 验证参数
	taskID, err := validateStringParam(request, "taskID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// 调用任务服务
	t, err := s.services.Task().GetByID(ctx, strings.TrimSpace(taskID))
	if err != nil {
		return formatError("Failed to get task", err), nil
	}

	return formatSuccess(t), nil
}

// Kubernetes 资源处理函数

// handleGetClusterPods handles the get_cluster_pods tool call
func (s *MCPServer) handleGetClusterPods(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	ctrl, err := s.getController(ctx, request)
	if err != nil {
		return formatError("Failed to get controller", err), nil
	}

	pods, err := ctrl.ListPodToString(ctx, controller.ListPodsOptions{})
	if err != nil {
		return formatError("Failed to get pods", err), nil
	}

	return formatTextSuccess(pods), nil
}

// handleGetClusterDeployment handles the get_cluster_deployment tool call
func (s *MCPServer) handleGetClusterDeployment(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	ctrl, err := s.getController(ctx, request)
	if err != nil {
		return formatError("Failed to get controller", err), nil
	}

	deploy, err := ctrl.GetDeployment(ctx, controller.GetDeploymentOptions{})
	if err != nil {
		return formatError("Failed to get deployment", err), nil
	}

	deploy.ManagedFields = nil
	return formatSuccess(deploy), nil
}

// handleGetClusterLeader handles the get_cluster_leader tool call
func (s *MCPServer) handleGetClusterLeader(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	ctrl, err := s.getController(ctx, request)
	if err != nil {
		return formatError("Failed to get controller", err), nil
	}

	pod, err := ctrl.GetLeaderPod(ctx)
	if err != nil {
		return formatError("Failed to get leader pod", err), nil
	}

	pod.ManagedFields = nil
	return formatSuccess(pod), nil
}

// handleGetClusterConfig handles the get_cluster_config tool call
func (s *MCPServer) handleGetClusterConfig(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	ctrl, err := s.getController(ctx, request)
	if err != nil {
		return formatError("Failed to get controller", err), nil
	}

	cm, err := ctrl.GetConfig(ctx)
	if err != nil {
		return formatError("Failed to get config", err), nil
	}

	return formatSuccess(cm), nil
}

// handleGetClusterLogs handles the get_cluster_logs tool call
func (s *MCPServer) handleGetClusterLogs(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	ctrl, err := s.getController(ctx, request)
	if err != nil {
		return formatError("Failed to get controller", err), nil
	}

	_, err = validateOptionalStringParam(request, "sinceTime")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	pod, err := ctrl.GetLeaderPod(ctx)
	if err != nil {
		return formatError("Failed to get leader pod", err), nil
	}

	logOptions := controller.PodLogOptions{
		PodName: pod.Name,
	}
	// Note: sinceTime parsing would need proper time.Time conversion
	// For now, we'll skip the sinceTime parameter

	logs, err := ctrl.GetLogs(ctx, logOptions)
	if err != nil {
		return formatError("Failed to get logs", err), nil
	}

	logText := ""
	if podLogs, exists := logs[pod.Name]; exists {
		logText = strings.Join(podLogs, "\n")
	}

	return formatTextSuccess(logText), nil
}

// 辅助函数

// getController 从请求中获取控制器实例
func (s *MCPServer) getController(ctx context.Context, request mcp.CallToolRequest) (*controller.Controller, error) {
	clusterID, err := validateStringParam(request, "clusterID")
	if err != nil {
		return nil, err
	}

	controllerName, err := validateStringParam(request, "controllerName")
	if err != nil {
		return nil, err
	}

	user, _ := validateOptionalStringParam(request, "user")
	token, _ := validateOptionalStringParam(request, "token")

	// 构建目标
	target := &kateway.Target{
		Name:    strings.TrimSpace(controllerName),
		Cluster: strings.TrimSpace(clusterID),
		User:    strings.TrimSpace(user),
		Token:   strings.TrimSpace(token),
	}

	// 获取控制器实例
	var ctrl *controller.Instance
	if target.Token != "" {
		ctrl, err = controller.GetByClusterIDWithIanvs(ctx, target.Cluster, target.User, target.Token)
	} else {
		ctrl, err = controller.GetByClusterID(ctx, target.Cluster)
	}
	if err != nil {
		return nil, err
	}

	// 根据名称选择控制器
	switch target.Name {
	case "service":
		return ctrl.Service(), nil
	case "ingress":
		return ctrl.Ingress(), nil
	default:
		return nil, fmt.Errorf("unsupported controller name: %s", target.Name)
	}
}

// parseTaskFilters 解析任务过滤器字符串
func parseTaskFilters(filtersStr string) ([]task.ListOption, error) {
	var opts []task.ListOption

	// 简单的过滤器解析：key1=value1,key2=value2
	pairs := strings.Split(filtersStr, ",")
	for _, pair := range pairs {
		parts := strings.SplitN(strings.TrimSpace(pair), "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		switch key {
		case "ID":
			opts = append(opts, task.WithTaskIDs(value))
		case "Type":
			opts = append(opts, task.WithTypes(value))
		case "State":
			opts = append(opts, task.WithStates(model.TaskState(value)))
		case "ParentID":
			opts = append(opts, task.WithParentIDs(value))
		case "SubType":
			opts = append(opts, task.WithSubTypes(value))
		}
	}

	return opts, nil
}
