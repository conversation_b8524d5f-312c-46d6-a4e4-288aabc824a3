package mcp

import (
	"encoding/json"
	"testing"

	"github.com/mark3labs/mcp-go/mcp"
)

func TestMCPServerCreation(t *testing.T) {
	server := NewMCPServer()
	if server == nil {
		t.Fatal("Expected server to be created, got nil")
	}

	if server.server == nil {
		t.Fatal("Expected underlying MCP server to be created, got nil")
	}

	if server.services == nil {
		t.Fatal("Expected services to be initialized, got nil")
	}
}

func TestValidateStringParam(t *testing.T) {
	tests := []struct {
		name        string
		args        map[string]interface{}
		paramName   string
		expectError bool
		expectValue string
	}{
		{
			name:        "valid string parameter",
			args:        map[string]interface{}{"test": "value"},
			paramName:   "test",
			expectError: false,
			expectValue: "value",
		},
		{
			name:        "missing parameter",
			args:        map[string]interface{}{},
			paramName:   "test",
			expectError: true,
			expectValue: "",
		},
		{
			name:        "empty string parameter",
			args:        map[string]interface{}{"test": ""},
			paramName:   "test",
			expectError: true,
			expectValue: "",
		},
		{
			name:        "non-string parameter",
			args:        map[string]interface{}{"test": 123},
			paramName:   "test",
			expectError: true,
			expectValue: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock CallToolRequest
			request := mcp.CallToolRequest{
				Params: mcp.CallToolParams{
					Arguments: tt.args,
				},
			}

			value, err := validateStringParam(request, tt.paramName)

			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if value != tt.expectValue {
				t.Errorf("Expected value %q but got %q", tt.expectValue, value)
			}
		})
	}
}

func TestValidateOptionalBoolParam(t *testing.T) {
	tests := []struct {
		name        string
		args        map[string]interface{}
		paramName   string
		expectError bool
		expectValue bool
	}{
		{
			name:        "valid bool parameter true",
			args:        map[string]interface{}{"test": true},
			paramName:   "test",
			expectError: false,
			expectValue: true,
		},
		{
			name:        "valid bool parameter false",
			args:        map[string]interface{}{"test": false},
			paramName:   "test",
			expectError: false,
			expectValue: false,
		},
		{
			name:        "missing parameter",
			args:        map[string]interface{}{},
			paramName:   "test",
			expectError: false,
			expectValue: false,
		},
		{
			name:        "non-bool parameter",
			args:        map[string]interface{}{"test": "true"},
			paramName:   "test",
			expectError: true,
			expectValue: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock CallToolRequest
			request := mcp.CallToolRequest{
				Params: mcp.CallToolParams{
					Arguments: tt.args,
				},
			}

			value, err := validateOptionalBoolParam(request, tt.paramName)

			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if value != tt.expectValue {
				t.Errorf("Expected value %v but got %v", tt.expectValue, value)
			}
		})
	}
}

func TestFormatSuccess(t *testing.T) {
	testData := map[string]interface{}{
		"message": "success",
		"count":   42,
	}

	result := formatSuccess(testData)
	if result == nil {
		t.Fatal("Expected result to be non-nil")
	}

	// The result should be a text result containing JSON
	if result.Content == nil {
		t.Fatal("Expected result content to be non-nil")
	}

	// Parse the JSON content to verify it's valid
	var parsed map[string]interface{}
	err := json.Unmarshal([]byte(result.Content[0].(mcp.TextContent).Text), &parsed)
	if err != nil {
		t.Fatalf("Expected valid JSON in result, got error: %v", err)
	}

	if parsed["message"] != "success" {
		t.Errorf("Expected message 'success', got %v", parsed["message"])
	}
	if parsed["count"] != float64(42) { // JSON numbers are float64
		t.Errorf("Expected count 42, got %v", parsed["count"])
	}
}

func TestFormatError(t *testing.T) {
	result := formatError("test error", nil)
	if result == nil {
		t.Fatal("Expected result to be non-nil")
	}

	if !result.IsError {
		t.Error("Expected result to be an error")
	}

	if result.Content == nil {
		t.Fatal("Expected result content to be non-nil")
	}

	textContent := result.Content[0].(mcp.TextContent)
	if textContent.Text != "test error" {
		t.Errorf("Expected error message 'test error', got %q", textContent.Text)
	}
}
