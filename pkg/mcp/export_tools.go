package mcp

import (
	"context"
	"encoding/base64"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
)

// registerExportTools registers all data export-related MCP tools
func (s *MCPServer) registerExportTools() {
	// Download cluster report tool
	s.addTool(Tool{
		Definition: mcp.NewTool("download_cluster_report",
			mcp.WithDescription("Download cluster scan data in CSV format"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID to download report for"),
			),
			mcp.WithBoolean("refreshCache",
				mcp.Description("Whether to force refresh the cache (default: false)"),
			),
		),
		Handler: s.handleDownloadClusterReport,
	})

	// Download risk report tool
	s.addTool(Tool{
		Definition: mcp.NewTool("download_risk_report",
			mcp.WithDescription("Download risk data in CSV format"),
			mcp.WithString("state",
				mcp.Description("Filter risks by state: 'stock' for existing risks, 'increment' for new risks (optional)"),
				mcp.Enum("stock", "increment"),
			),
		),
		Handler: s.handleDownloadRiskReport,
	})
}

// handleDownloadClusterReport handles the download_cluster_report tool call
func (s *MCPServer) handleDownloadClusterReport(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate parameters
	clusterID, err := validateStringParam(request, "clusterID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	refreshCache, err := validateOptionalBoolParam(request, "refreshCache")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get cluster reference
	cluster, err := s.services.Cluster().Get(ctx, strings.TrimSpace(clusterID))
	if err != nil {
		return formatError("Failed to get cluster", err), nil
	}

	// Get cluster risk report for CSV export
	report, err := s.services.Inspection().GetClusterRiskReport(ctx, cluster, refreshCache)
	if err != nil {
		return formatError("Failed to get cluster risk report", err), nil
	}

	// Get CSV data
	csvData := report.CSV()

	// Encode CSV data as base64 for safe transport
	encodedData := base64.StdEncoding.EncodeToString(csvData)

	// Return response with metadata
	response := map[string]interface{}{
		"filename":    cluster.ClusterID + "-cluster-report.csv",
		"contentType": "text/csv",
		"size":        len(csvData),
		"data":        encodedData,
		"encoding":    "base64",
	}

	return formatSuccess(response), nil
}

// handleDownloadRiskReport handles the download_risk_report tool call
func (s *MCPServer) handleDownloadRiskReport(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate optional parameters
	state, err := validateOptionalStringParam(request, "state")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get risk report
	report, err := s.services.LegacyTask().GetRiskReport(ctx)
	if err != nil {
		return formatError("Failed to get risk report", err), nil
	}

	// Filter by state if specified
	if state == "stock" {
		report.Increment = nil
	} else if state == "increment" {
		report.Stock = nil
	}

	// Get CSV data
	csvData := report.CSV()

	// Encode CSV data as base64 for safe transport
	encodedData := base64.StdEncoding.EncodeToString(csvData)

	// Determine filename based on state
	filename := "risk-report.csv"
	if state != "" {
		filename = "risk-report-" + state + ".csv"
	}

	// Return response with metadata
	response := map[string]interface{}{
		"filename":    filename,
		"contentType": "text/csv",
		"size":        len(csvData),
		"data":        encodedData,
		"encoding":    "base64",
	}

	return formatSuccess(response), nil
}
