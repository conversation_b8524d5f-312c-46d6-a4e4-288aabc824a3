package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"

	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
)

// MCPServer wraps the MCP server with our business logic
type MCPServer struct {
	server   *server.MCPServer
	services *services.Services
}

// NewMCPServer creates a new MCP server instance
func NewMCPServer() *MCPServer {
	// Create the underlying MCP server
	s := server.NewMCPServer(
		"Kateway MCP Server",
		"1.0.0",
		server.WithToolCapabilities(true),
		server.WithRecovery(),
	)

	mcpServer := &MCPServer{
		server:   s,
		services: services.Get(),
	}

	// Register all tools
	mcpServer.registerTools()

	return mcpServer
}

// registerTools registers all available MCP tools
func (s *MCPServer) registerTools() {
	// Register cluster information tools
	s.registerClusterTools()

	// Register CLB tools
	s.registerCLBTools()

	// Register resource tools
	s.registerResourceTools()

	// Register task and risk tools
	s.registerTaskTools()

	// Register export tools
	s.registerExportTools()

	// Register gRPC-based tools
	s.registerGRPCTools()
}

// Serve starts the MCP server using stdio transport
func (s *MCPServer) Serve() error {
	log.Printf("Starting Kateway MCP Server...")
	return server.ServeStdio(s.server)
}

// ServeHTTP starts the MCP server using HTTP transport
func (s *MCPServer) ServeHTTP(addr string) error {
	log.Printf("Starting Kateway MCP Server on HTTP %s...", addr)
	httpServer := server.NewStreamableHTTPServer(s.server)
	return httpServer.Start(addr)
}

// GetServer returns the underlying MCP server for advanced usage
func (s *MCPServer) GetServer() *server.MCPServer {
	return s.server
}

// Tool represents a MCP tool with its handler
type Tool struct {
	Definition mcp.Tool
	Handler    func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)
}

// addTool is a helper method to add a tool to the server with middleware
func (s *MCPServer) addTool(tool Tool) {
	// Wrap the handler with middleware
	wrappedHandler := s.wrapWithMiddleware(tool.Handler)
	s.server.AddTool(tool.Definition, wrappedHandler)
}

// ToolMiddleware represents a middleware function for tool handlers
type ToolMiddleware func(next func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)

// wrapWithMiddleware applies middleware to a tool handler
func (s *MCPServer) wrapWithMiddleware(handler func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Apply logging middleware
	wrapped := loggingMiddleware(handler)

	// Apply error recovery middleware
	wrapped = recoveryMiddleware(wrapped)

	// Apply timeout middleware
	wrapped = timeoutMiddleware(wrapped)

	return wrapped
}

// loggingMiddleware logs tool calls and their results
func loggingMiddleware(next func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		toolName := request.Params.Name
		log.Printf("MCP Tool Call: %s with args: %v", toolName, request.GetArguments())

		result, err := next(ctx, request)

		if err != nil {
			log.Printf("MCP Tool Error: %s failed with error: %v", toolName, err)
		} else if result != nil && result.IsError {
			log.Printf("MCP Tool Error: %s returned error result", toolName)
		} else {
			log.Printf("MCP Tool Success: %s completed successfully", toolName)
		}

		return result, err
	}
}

// recoveryMiddleware recovers from panics in tool handlers
func recoveryMiddleware(next func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return func(ctx context.Context, request mcp.CallToolRequest) (result *mcp.CallToolResult, err error) {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("MCP Tool Panic: %s panicked with: %v", request.Params.Name, r)
				result = mcp.NewToolResultError(fmt.Sprintf("Internal error occurred while executing tool: %v", r))
				err = nil
			}
		}()

		return next(ctx, request)
	}
}

// timeoutMiddleware adds timeout handling to tool calls
func timeoutMiddleware(next func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error)) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// Check if context already has a timeout
		if _, hasDeadline := ctx.Deadline(); hasDeadline {
			return next(ctx, request)
		}

		// Add a default timeout of 30 seconds for tool calls
		timeoutCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
		defer cancel()

		// Use a channel to handle the result and potential timeout
		resultChan := make(chan struct {
			result *mcp.CallToolResult
			err    error
		}, 1)

		go func() {
			result, err := next(timeoutCtx, request)
			resultChan <- struct {
				result *mcp.CallToolResult
				err    error
			}{result, err}
		}()

		select {
		case res := <-resultChan:
			return res.result, res.err
		case <-timeoutCtx.Done():
			log.Printf("MCP Tool Timeout: %s timed out after 30 seconds", request.Params.Name)
			return mcp.NewToolResultError("Tool execution timed out"), nil
		}
	}
}

// validateStringParam validates and extracts a required string parameter
func validateStringParam(request mcp.CallToolRequest, paramName string) (string, error) {
	value, err := request.RequireString(paramName)
	if err != nil {
		return "", fmt.Errorf("parameter '%s' is required and must be a string: %w", paramName, err)
	}
	if value == "" {
		return "", fmt.Errorf("parameter '%s' cannot be empty", paramName)
	}
	return value, nil
}

// validateOptionalStringParam validates and extracts an optional string parameter
func validateOptionalStringParam(request mcp.CallToolRequest, paramName string) (string, error) {
	args := request.GetArguments()
	if value, exists := args[paramName]; exists {
		if strValue, ok := value.(string); ok {
			return strValue, nil
		}
		return "", fmt.Errorf("parameter '%s' must be a string", paramName)
	}
	return "", nil
}

// validateOptionalBoolParam validates and extracts an optional boolean parameter
func validateOptionalBoolParam(request mcp.CallToolRequest, paramName string) (bool, error) {
	args := request.GetArguments()
	if value, exists := args[paramName]; exists {
		if boolValue, ok := value.(bool); ok {
			return boolValue, nil
		}
		return false, fmt.Errorf("parameter '%s' must be a boolean", paramName)
	}
	return false, nil
}

// validateOptionalIntParam validates and extracts an optional integer parameter
func validateOptionalIntParam(request mcp.CallToolRequest, paramName string) (int, error) {
	args := request.GetArguments()
	if value, exists := args[paramName]; exists {
		if intValue, ok := value.(int); ok {
			return intValue, nil
		}
		if floatValue, ok := value.(float64); ok {
			return int(floatValue), nil
		}
		return 0, fmt.Errorf("parameter '%s' must be an integer", paramName)
	}
	return 0, nil
}

// validateRequiredIntParam validates and extracts a required integer parameter
func validateRequiredIntParam(request mcp.CallToolRequest, paramName string) (int, error) {
	args := request.GetArguments()
	value, exists := args[paramName]
	if !exists {
		return 0, fmt.Errorf("parameter '%s' is required", paramName)
	}

	if intValue, ok := value.(int); ok {
		return intValue, nil
	}
	if floatValue, ok := value.(float64); ok {
		return int(floatValue), nil
	}
	return 0, fmt.Errorf("parameter '%s' must be an integer", paramName)
}

// formatError creates a standardized error response
func formatError(message string, err error) *mcp.CallToolResult {
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("%s: %v", message, err))
	}
	return mcp.NewToolResultError(message)
}

// formatSuccess creates a standardized success response with JSON data
func formatSuccess(data interface{}) *mcp.CallToolResult {
	// Convert data to JSON string for text response
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal response data: %v", err))
	}
	return mcp.NewToolResultText(string(jsonBytes))
}

// formatTextSuccess creates a standardized success response with text data
func formatTextSuccess(text string) *mcp.CallToolResult {
	return mcp.NewToolResultText(text)
}
