package mcp

import (
	"context"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
)

// registerCLBTools registers all CLB-related MCP tools
func (s *MCPServer) registerCLBTools() {
	// Get CLB report tool
	s.addTool(Tool{
		Definition: mcp.NewTool("get_clb_report",
			mcp.WithDescription("Get health check report for a specific CLB (Cloud Load Balancer)"),
			mcp.WithString("clusterID",
				mcp.Required(),
				mcp.Description("The cluster ID where the CLB is located"),
			),
			mcp.WithString("clbID",
				mcp.Required(),
				mcp.Description("The CLB ID to get report for"),
			),
			mcp.WithBoolean("refreshCache",
				mcp.Description("Whether to force refresh the cache (default: false)"),
			),
			mcp.WithBoolean("includeBackend",
				mcp.Description("Whether to include backend server information (default: false)"),
			),
		),
		Handler: s.handleGetCLBReport,
	})
}

// handleGetCLBReport handles the get_clb_report tool call
func (s *MCPServer) handleGetCLBReport(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Validate parameters
	clusterID, err := validateStringParam(request, "clusterID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	clbID, err := validateStringParam(request, "clbID")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	refreshCache, err := validateOptionalBoolParam(request, "refreshCache")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	includeBackend, err := validateOptionalBoolParam(request, "includeBackend")
	if err != nil {
		return formatError("Invalid parameters", err), nil
	}

	// Get cluster reference
	cluster, err := s.services.Cluster().Get(ctx, strings.TrimSpace(clusterID))
	if err != nil {
		return formatError("Failed to get cluster", err), nil
	}

	// Get CLB report
	report, err := s.services.Inspection().GetCLBReport(ctx, cluster, strings.TrimSpace(clbID), refreshCache, includeBackend)
	if err != nil {
		return formatError("Failed to get CLB report", err), nil
	}

	return formatSuccess(report), nil
}
