# MCP 服务转换需求文档

## 介绍

将 kateway-server 项目的 HTTP server 接口转换成 MCP (Model Context Protocol) 服务，使 MCP Client 能够通过标准的 MCP 协议调用服务器提供的各种工具，实现集群信息查询、CLB 信息查询、资源信息查询、联合排障等能力。

## 需求

### 需求 1 - 集群信息查询工具

**用户故事：** 作为 MCP Client 用户，我希望能够通过 MCP 协议查询集群信息，包括集群列表、集群详情和集群健康检查报告。

#### 验收标准

1. When MCP Client 调用 `list_cluster_info` 工具时，系统应当返回指定 AppID 下的所有集群信息列表
2. When MCP Client 调用 `get_cluster_info` 工具时，系统应当返回指定集群 ID 的详细信息
3. When MCP Client 调用 `get_cluster_report` 工具时，系统应当返回指定集群的健康检查报告
4. When MCP Client 调用 `get_cluster_risk_report` 工具时，系统应当返回指定集群的风险报告
5. When 调用时指定 `refreshCache=true` 参数时，系统应当强制刷新缓存并返回最新数据

### 需求 2 - CLB 信息查询工具

**用户故事：** 作为 MCP Client 用户，我希望能够通过 MCP 协议查询 CLB (Cloud Load Balancer) 相关信息。

#### 验收标准

1. When MCP Client 调用 `get_clb_report` 工具时，系统应当返回指定集群和 CLB ID 的负载均衡器健康检查报告
2. When 指定 `includeBackend=true` 参数时，系统应当包含后端服务器的详细信息
3. When 调用时指定 `refreshCache=true` 参数时，系统应当强制刷新缓存并返回最新数据

### 需求 3 - 资源信息查询工具

**用户故事：** 作为 MCP Client 用户，我希望能够通过 MCP 协议查询 Kubernetes 资源信息。

#### 验收标准

1. When MCP Client 调用 `get_resource_report` 工具时，系统应当返回指定集群、资源类型、命名空间和资源名称的资源健康检查报告
2. When 调用时指定 `refreshCache=true` 参数时，系统应当强制刷新缓存并返回最新数据

### 需求 4 - 联合排障工具

**用户故事：** 作为 MCP Client 用户，我希望能够通过 MCP 协议进行联合排障，查询任务和风险信息。

#### 验收标准

1. When MCP Client 调用 `get_task_report` 工具时，系统应当返回健康检查任务列表
2. When MCP Client 调用 `get_risk_report` 工具时，系统应当返回巡检风险列表
3. When 指定 `state` 参数时，系统应当根据状态过滤任务或风险信息
4. When 指定 `clusterID` 参数时，系统应当返回指定集群的任务信息

### 需求 5 - 数据导出工具

**用户故事：** 作为 MCP Client 用户，我希望能够通过 MCP 协议导出集群扫描数据和风险数据。

#### 验收标准

1. When MCP Client 调用 `download_cluster_report` 工具时，系统应当返回指定集群的 CSV 格式扫描数据
2. When MCP Client 调用 `download_risk_report` 工具时，系统应当返回 CSV 格式的风险数据
3. When 指定 `state` 参数时，系统应当根据状态过滤导出的风险数据

### 需求 6 - MCP 服务框架

**用户故事：** 作为系统管理员，我希望有一个标准的 MCP 服务框架来支持上述工具的实现。

#### 验收标准

1. When MCP Server 启动时，系统应当注册所有可用的工具
2. When MCP Client 连接时，系统应当提供工具列表和描述信息
3. When MCP Client 调用工具时，系统应当验证参数并返回结构化的响应
4. When 发生错误时，系统应当返回标准的 MCP 错误响应
5. When 服务运行时，系统应当支持并发请求处理
