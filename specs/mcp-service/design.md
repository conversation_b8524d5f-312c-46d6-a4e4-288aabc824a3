# MCP 服务技术方案设计

## 技术架构

### 整体架构

```mermaid
graph TB
    A[MCP Client] -->|MCP Protocol| B[MCP Server]
    B --> C[Tool Registry]
    B --> D[Request Handler]
    D --> E[Service Layer]
    E --> F[Existing HTTP Services]
    
    C --> G[Cluster Tools]
    C --> H[CLB Tools]
    C --> I[Resource Tools]
    C --> J[Task Tools]
    
    F --> K[Inspection Service]
    F --> L[Cluster Service]
    F --> M[Legacy Task Service]
```

### 技术栈

- **MCP 协议实现**: 基于 JSON-RPC 2.0 over stdio/HTTP
- **编程语言**: Go 1.21+
- **Web 框架**: 复用现有的 Gin 框架
- **数据序列化**: JSON
- **并发处理**: Go goroutines + context
- **错误处理**: 标准 MCP 错误格式

## 核心组件设计

### 1. MCP Server 核心

```go
type MCPServer struct {
    tools    map[string]Tool
    services *services.Services
}

type Tool interface {
    Name() string
    Description() string
    InputSchema() map[string]interface{}
    Execute(ctx context.Context, params map[string]interface{}) (*ToolResult, error)
}
```

### 2. 工具注册系统

每个工具实现 `Tool` 接口，支持：
- 工具名称和描述
- 输入参数 JSON Schema 验证
- 异步执行支持
- 错误处理

### 3. 服务适配层

将现有的 HTTP 服务接口适配为 MCP 工具：
- `services.Get().Inspection()` -> 集群相关工具
- `services.Get().Cluster()` -> 集群基础信息工具
- `services.Get().LegacyTask()` -> 任务和风险工具

## 工具设计

### 集群信息工具

1. **list_cluster_info**
   - 参数: `appID` (string, required)
   - 返回: 集群列表和统计信息

2. **get_cluster_info**
   - 参数: `clusterID` (string, required), `refreshCache` (boolean, optional)
   - 返回: 集群详细信息

3. **get_cluster_report**
   - 参数: `clusterID` (string, required), `filter` (string, optional), `refreshCache` (boolean, optional)
   - 返回: 集群健康检查报告

### CLB 信息工具

4. **get_clb_report**
   - 参数: `clusterID` (string, required), `clbID` (string, required), `refreshCache` (boolean, optional), `includeBackend` (boolean, optional)
   - 返回: CLB 健康检查报告

### 资源信息工具

5. **get_resource_report**
   - 参数: `clusterID`, `resourceType`, `resourceNamespace`, `resourceName` (all required), `refreshCache` (optional)
   - 返回: 资源健康检查报告

### 联合排障工具

6. **get_task_report**
   - 参数: `clusterID` (optional), `state` (optional)
   - 返回: 任务报告

7. **get_risk_report**
   - 参数: `state` (optional)
   - 返回: 风险报告

### 数据导出工具

8. **download_cluster_report**
   - 参数: `clusterID` (required), `refreshCache` (optional)
   - 返回: CSV 格式的集群扫描数据

9. **download_risk_report**
   - 参数: `state` (optional)
   - 返回: CSV 格式的风险数据

## 数据流设计

1. **请求处理流程**:
   ```
   MCP Client Request -> JSON-RPC Parser -> Tool Router -> Parameter Validation -> Service Call -> Response Formatting -> MCP Client
   ```

2. **错误处理流程**:
   ```
   Service Error -> Error Mapping -> MCP Error Format -> Client Response
   ```

3. **并发处理**:
   - 每个工具调用在独立的 goroutine 中执行
   - 使用 context 进行超时和取消控制
   - 支持多个客户端并发连接

## 安全性设计

1. **参数验证**: 使用 JSON Schema 验证所有输入参数
2. **权限控制**: 复用现有的服务层权限验证
3. **资源限制**: 设置请求超时和并发限制
4. **错误信息**: 避免泄露敏感的系统信息

## 测试策略

1. **单元测试**: 每个工具的独立测试
2. **集成测试**: MCP 协议端到端测试
3. **性能测试**: 并发请求和大数据量测试
4. **兼容性测试**: 与现有 HTTP API 的对比测试

## 部署方案

1. **独立服务**: 作为独立的 MCP 服务运行
2. **集成部署**: 与现有 kateway-server 集成部署
3. **配置管理**: 支持通过环境变量配置服务参数
4. **监控告警**: 集成现有的监控体系
