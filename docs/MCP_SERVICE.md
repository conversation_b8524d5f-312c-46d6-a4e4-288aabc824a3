# Kateway MCP 服务

Kateway MCP 服务将 Kateway Server 的集群管理、CLB 分析、资源监控和联合排障功能通过 Model Context Protocol (MCP) 协议暴露给 AI 客户端。

## 功能特性

- **集群信息查询**: 列出集群、获取集群详情和健康检查报告
- **CLB 信息查询**: 获取负载均衡器健康检查报告
- **资源信息查询**: 获取 Kubernetes 资源健康检查报告
- **联合排障**: 获取任务和风险报告
- **数据导出**: 下载 CSV 格式的扫描和风险数据
- **并发处理**: 支持多客户端并发连接
- **中间件支持**: 内置日志记录、错误恢复和超时处理

## 配置

在 `kops.yaml` 配置文件中添加 MCP 配置：

```yaml
mcp:
  enabled: true      # 是否启用 MCP 服务
  port: 8081        # MCP 服务端口
  pathPrefix: "/mcp" # HTTP 信息接口路径前缀
```

## 服务启动

MCP 服务会随 Kateway Server 自动启动：

1. **MCP 服务器**: 在配置的端口（默认 8081）上启动，提供完整的 MCP 协议支持
2. **信息接口**: 在主 HTTP 服务器（端口 80）的 `/mcp` 路径提供服务状态信息

## 可用工具

### 原有集群管理工具

#### `list_cluster_info`
列出指定 AppID 下的所有集群。

**参数:**
- `appID` (string, 必需): 要列出集群的 AppID

**示例:**
```json
{
  "name": "list_cluster_info",
  "arguments": {
    "appID": "1234567890"
  }
}
```

#### `get_cluster_info`
获取指定集群的详细信息。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `refreshCache` (boolean, 可选): 是否强制刷新缓存

#### `get_cluster_report`
获取指定集群的健康检查报告。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `filter` (string, 可选): 报告过滤器，如 "risk"
- `refreshCache` (boolean, 可选): 是否强制刷新缓存

#### `get_cluster_risk_report`
获取指定集群的风险评估报告。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `refreshCache` (boolean, 可选): 是否强制刷新缓存

### CLB 管理工具

#### `get_clb_report`
获取指定 CLB 的健康检查报告。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `clbID` (string, 必需): CLB ID
- `refreshCache` (boolean, 可选): 是否强制刷新缓存
- `includeBackend` (boolean, 可选): 是否包含后端服务器信息

### 资源管理工具

#### `get_resource_report`
获取指定 Kubernetes 资源的健康检查报告。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `resourceType` (string, 必需): 资源类型
- `resourceNamespace` (string, 必需): 资源命名空间
- `resourceName` (string, 必需): 资源名称
- `refreshCache` (boolean, 可选): 是否强制刷新缓存

### 联合排障工具

#### `get_task_report`
获取健康检查任务报告。

**参数:**
- `clusterID` (string, 可选): 按集群 ID 过滤
- `state` (string, 可选): 按状态过滤

#### `get_risk_report`
获取巡检风险报告。

**参数:**
- `state` (string, 可选): 过滤状态，"stock" 或 "increment"

### 数据导出工具

#### `download_cluster_report`
下载集群扫描数据（CSV 格式）。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `refreshCache` (boolean, 可选): 是否强制刷新缓存

#### `download_risk_report`
下载风险数据（CSV 格式）。

**参数:**
- `state` (string, 可选): 过滤状态，"stock" 或 "increment"

## 客户端连接

### HTTP 连接

```go
import "github.com/mark3labs/mcp-go/client"

mcpClient, err := client.NewHTTPClient("http://localhost:8081")
if err != nil {
    log.Fatal(err)
}
defer mcpClient.Close()
```

### 调用工具

```go
result, err := mcpClient.CallTool(ctx, mcp.CallToolRequest{
    Params: mcp.CallToolParams{
        Name: "list_cluster_info",
        Arguments: map[string]interface{}{
            "appID": "1234567890",
        },
    },
})
```

## 示例代码

参考 `examples/mcp-client/main.go` 获取完整的客户端示例代码。

## 错误处理

所有工具调用都包含标准化的错误处理：

- **参数验证错误**: 当必需参数缺失或类型不正确时返回
- **服务错误**: 当底层服务调用失败时返回
- **超时错误**: 当工具执行超过 30 秒时返回
- **恢复错误**: 当工具执行发生 panic 时返回

## 监控和日志

MCP 服务包含内置的监控和日志功能：

- **请求日志**: 记录所有工具调用和结果
- **错误日志**: 记录所有错误和异常
- **性能监控**: 记录工具执行时间
- **并发控制**: 支持多客户端并发访问

### 新增 gRPC 工具

#### CLB 直接查询工具

#### `get_clb_by_id`
直接通过 LB ID、VIP 或域名获取 CLB 信息。

**参数:**
- `query` (string, 必需): CLB 查询字符串，支持 LB ID (lb-xxx)、VIP 地址或域名

#### `get_clb_listeners`
直接通过 LB ID、VIP 或域名获取 CLB 监听器信息。

**参数:**
- `query` (string, 必需): CLB 查询字符串

#### `get_clb_backends`
直接通过 LB ID、VIP 或域名获取 CLB 后端服务器信息。

**参数:**
- `query` (string, 必需): CLB 查询字符串

#### `get_clb_health`
直接通过 LB ID、VIP 或域名获取 CLB 健康检查状态。

**参数:**
- `query` (string, 必需): CLB 查询字符串

#### 用户和集群信息工具

#### `get_user_info`
通过 UIN 或 AppID 获取用户信息。

**参数:**
- `query` (string, 必需): 用户查询字符串，UIN 或 AppID

#### `get_cluster_by_id`
直接通过集群 ID 获取集群信息。

**参数:**
- `clusterID` (string, 必需): 集群 ID

#### 镜像检查工具

#### `check_image`
检查容器镜像是否在指定地域存在。

**参数:**
- `name` (string, 必需): 组件名称 (如 'service', 'ingress')
- `version` (string, 必需): 要检查的镜像版本
- `region` (string, 可选): 指定地域，不指定则检查所有地域

#### 任务管理工具

#### `list_tasks`
列出任务，支持可选过滤器。

**参数:**
- `filters` (string, 可选): 过滤器字符串，格式为 'key1=value1,key2=value2'
- `limit` (number, 可选): 返回的最大任务数
- `offset` (number, 可选): 跳过的任务数

#### `get_task_by_id`
通过任务 ID 获取详细任务信息。

**参数:**
- `taskID` (string, 必需): 任务 ID

#### Kubernetes 资源工具

#### `get_cluster_pods`
获取指定集群的 Pod 信息。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `controllerName` (string, 必需): 控制器名称 ('service' 或 'ingress')
- `user` (string, 可选): 用户名
- `token` (string, 可选): 访问令牌

#### `get_cluster_deployment`
获取指定集群的 Deployment 信息。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `controllerName` (string, 必需): 控制器名称 ('service' 或 'ingress')
- `user` (string, 可选): 用户名
- `token` (string, 可选): 访问令牌

#### `get_cluster_leader`
获取指定集群的 Leader Pod 信息。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `controllerName` (string, 必需): 控制器名称 ('service' 或 'ingress')
- `user` (string, 可选): 用户名
- `token` (string, 可选): 访问令牌

#### `get_cluster_config`
获取指定集群的配置信息。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `controllerName` (string, 必需): 控制器名称 ('service' 或 'ingress')
- `user` (string, 可选): 用户名
- `token` (string, 可选): 访问令牌

#### `get_cluster_logs`
获取集群 Leader Pod 的日志。

**参数:**
- `clusterID` (string, 必需): 集群 ID
- `controllerName` (string, 必需): 控制器名称 ('service' 或 'ingress')
- `sinceTime` (string, 可选): 获取此时间之后的日志 (RFC3339 格式)
- `user` (string, 可选): 用户名
- `token` (string, 可选): 访问令牌

## 部署注意事项

1. 确保 MCP 服务端口（默认 8081）在防火墙中开放
2. 配置适当的资源限制以处理并发请求
3. 监控服务日志以识别潜在问题
4. 定期检查服务状态接口 `http://localhost:80/mcp`
