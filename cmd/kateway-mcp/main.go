package main

import (
	"flag"
	"log"
	"os"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/pkg/mcp"
)

func main() {
	var (
		httpAddr = flag.String("http", "", "HTTP server address (e.g., :8080). If not specified, uses stdio transport")
		help     = flag.Bool("help", false, "Show help message")
	)
	flag.Parse()

	if *help {
		showHelp()
		return
	}

	// Initialize configuration
	if err := initConfig(); err != nil {
		log.Fatalf("Failed to initialize configuration: %v", err)
	}

	// Create MCP server
	server := mcp.NewMCPServer()

	// Start server based on transport type
	if *httpAddr != "" {
		// HTTP transport
		log.Printf("Starting Kateway MCP Server on HTTP %s", *httpAddr)
		if err := server.ServeHTTP(*httpAddr); err != nil {
			log.Fatalf("HTTP server error: %v", err)
		}
	} else {
		// Stdio transport (default for MCP)
		log.Printf("Starting Kateway MCP Server on stdio")
		if err := server.Serve(); err != nil {
			log.Fatalf("Stdio server error: %v", err)
		}
	}
}

func showHelp() {
	log.Printf(`Kateway MCP Server

This server exposes Kateway's cluster inspection, CLB analysis, resource monitoring,
and troubleshooting capabilities through the Model Context Protocol (MCP).

Available Tools:
  - list_cluster_info: List all clusters for a given AppID
  - get_cluster_info: Get detailed information for a specific cluster
  - get_cluster_report: Get health check report for a specific cluster
  - get_cluster_risk_report: Get risk assessment report for a specific cluster
  - get_clb_report: Get health check report for a specific CLB
  - get_resource_report: Get health check report for a specific Kubernetes resource
  - get_task_report: Get health check task report
  - get_risk_report: Get inspection risk report
  - download_cluster_report: Download cluster scan data in CSV format
  - download_risk_report: Download risk data in CSV format

Usage:
  %s [options]

Options:
`, os.Args[0])
	flag.PrintDefaults()
}

func initConfig() error {
	// Initialize the configuration system
	// This ensures that the services can access database and other dependencies
	cfg := config.Get()
	if cfg == nil {
		return nil // Config might be optional in some environments
	}
	return nil
}
