package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/jinzhu/configor"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gopkg.in/yaml.v2"

	"git.woa.com/kateway/pkg/database/mysql"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/options"
	"git.woa.com/kateway/kateway-server/pkg/tmp/tencentcloud/sts"
)

var globalConfig *Config

type Config struct {
	Task    TaskConfig `yaml:"task"`
	Checker struct {
		ControllerConfig `yaml:",inline"`
		Tag              struct {
			// 针对用户级别的并发度
			Concurrency int `yaml:"concurrency" default:"10"`
			// 需要处理的标签key值
			TargetKeys []string `yaml:"targetKeys" required:"true"`
			// 目标租户ID
			Appids []string `yaml:"appids"`
			// 单次执行某个用户删除标签的最大数量
			MaxDeletionNumPerUser int `yaml:"maxDeletionNumPerUser" default:"500"`
			// 当某个标签连续处于未使用状态超过该检查次数时，可以进行删除
			DeletionThreshold int  `yaml:"deletionThreshold" default:"30"`
			DeletionEnabled   bool `yaml:"deletionEnabled"`
		} `yaml:"tag"`
	} `yaml:"checker"`
	SkipCluster SkipCluster      `yaml:"skipCluster"`
	Service     Version          `yaml:"service" required:"true"`
	Ingress     Version          `yaml:"ingress" required:"true"`
	MySQL       mysql.Config     `yaml:"mysql" required:"true"`
	STKE        mysql.Config     `yaml:"stke" required:"true"`
	IANVS       mysql.Config     `yaml:"ianvs" required:"true"`
	TKEOSS      mysql.Config     `yaml:"tkeoss" required:"true"`
	STS         sts.Config       `yaml:"sts" required:"true"`
	Regions     []string         `yaml:"regions" required:"true"`
	LBCF        LBCFConifg       `yaml:"lbcf"`
	Inspection  InspectionConfig `yaml:"inspection"`
	KOPS        KOPS             `yaml:"kops"`
	MCP         MCPConfig        `yaml:"mcp"`
}

func LoadConfigFromFile(file string) (*Config, error) {
	data, err := os.ReadFile(file)
	if err != nil {
		return nil, errors.Wrapf(err, "read config file %s failed", file)
	}
	ret := &Config{}
	if err := yaml.Unmarshal(data, ret); err != nil {
		return nil, errors.Wrapf(err, "parse config file %s failed", file)
	}
	globalConfig = ret
	return ret, nil
}

type SkipCluster struct {
	ID   []string `yaml:"id"`
	Name []string `yaml:"name"`
}

func (c *Config) SkipClusterByID(id string) bool {
	return lo.Contains(c.SkipCluster.ID, id)
}

func (c *Config) SkipClusterByName(name string) bool {
	return lo.ContainsBy(c.SkipCluster.Name, func(n string) bool {
		return strings.Contains(name, n)
	})
}

type Version struct {
	MockVersion     string   `yaml:"mockVersion" required:"true"`
	ExpectedVersion string   `yaml:"expectedVersion" required:"true"`
	Versions        []string `yaml:"versions" required:"true"`
}

func Init(opts *options.Options) error {
	file, _ := filepath.Abs(opts.Conf)
	fmt.Printf("load config file: %s\n", file)
	config := &Config{}
	if err := configor.New(&configor.Config{
		Debug:     opts.Dev,
		ENVPrefix: "-",
	}).Load(config, file); err != nil {
		return fmt.Errorf("open config %s error: %w", file, err)
	}

	if opts.Dev {
		fmt.Println("config", spew.Sdump(config))
	}

	globalConfig = config

	return nil
}

func Get() *Config {
	if globalConfig != nil {
		return globalConfig
	}
	panic("global config not initial")
}

func (c *Config) GetVersion(name string) *Version {
	switch name {
	case "Service":
		return &c.Service
	case "Ingress":
		return &c.Ingress
	default:
		panic(fmt.Errorf("unsupport: %v", name))
	}
}

func (c *Config) GetImageName(name string) string {
	return fmt.Sprintf("ccr.ccs.tencentyun.com/paas/%s-controller", strings.ToLower(name))
}

type ControllerConfig struct {
	ResyncPeriod    time.Duration `yaml:"resyncPeriod" default:"10m"`
	ConcurrentSyncs int           `yaml:"concurrentSyncs" default:"100"`
	SyncTimeout     time.Duration `yaml:"syncTimeout" default:"5m"`
}

type STSConfig struct {
	SecretID   string `yaml:"secretID"`
	SecretKey  string `yaml:"secretKey"`
	RolePrefix string `yaml:"rolePrefix"`
	RoleName   string `yaml:"roleName"`
}

type TaskConfig struct {
	CLB        LBRConifg        `yaml:"clb"`
	LBR        LBRConifg        `yaml:"lbr"`
	LBCF       LBRConifg        `yaml:"lbcf"`
	MergeStats MergeStatsConfig `yaml:"mergeStats"`
	Merge      MergeConfig      `yaml:"merge"`
	Liveness   LivenessConfig   `yaml:"liveness"`
	Engine     TaskEngine       `yaml:"engine"`
}

type LivenessConfig struct {
	Interval time.Duration `yaml:"interval" default:"1h"`
	Clusters []string      `yaml:"clusters"`
	AppIDs   []string      `yaml:"appIDs"`
}

type MergeConfig struct {
	Enable          bool     `yaml:"enable"`
	SkippedClusters []string `yaml:"skippedClusters"`
	Limit           int      `yaml:"limit" default:"10"`
}

type TaskEngine struct {
	Concurrency     int           `yaml:"concurrency" default:"100"`
	TimeoutDuration time.Duration `yaml:"timeoutDuration" default:"10m"`
	ResyncInterval  time.Duration `yaml:"resyncInterval" default:"10s"`
}

type MergeStatsConfig struct {
	Concurrency int      `yaml:"concurrency" default:"100"`
	ClusterIDs  []string `yaml:"clusterIDs"`
	// version of the service controller ready for merge
	ServiceVersion string `yaml:"serviceVersion"`
}

type CLBConifg struct {
	ConcurrentSyncs int `yaml:"concurrentSyncs" default:"100"`
}

type LBRConifg struct {
	ConcurrentSyncs int `yaml:"concurrentSyncs" default:"100"`
}

type LBCFConifg struct {
	Clusters []string `yaml:"clusters"`
}

type MonitorConfig struct {
	Interval time.Duration `yaml:"interval" default:"1m"`
	Clusters []string      `yaml:"clusters"`
}

type InspectionConfig struct {
	MaxConcurrentSyncLBRPerCluster int           `yaml:"maxConcurrentSyncLBRPerCluster" default:"10"`
	MaxConcurrentRunningTaskCount  int           `yaml:"maxConcurrentRunningTaskCount" default:"20"`
	MinTaskSyncInterval            time.Duration `yaml:"minTaskSyncInterval" default:"1m"`
	RateLimit                      int           `yaml:"rateLimit" default:"50"`
}

type KOPS struct {
	Version        string     `yaml:"version"`
	DisallowUpdate ClusterSet `yaml:"disallowUpdate"`
	AllowUpdate    ClusterSet `yaml:"allowUpdate"`
}

type ClusterSet struct {
	Appids   []uint64 `yaml:"appids"`
	Clusters []string `yaml:"clusters"`
}

type MCPConfig struct {
	// MCP 服务端口，默认为 8081
	Port int `yaml:"port" default:"8081"`
	// 是否启用 MCP 服务，默认为 true
	Enabled bool `yaml:"enabled" default:"true"`
	// MCP 服务路径前缀，默认为 "/mcp"
	PathPrefix string `yaml:"pathPrefix" default:"/mcp"`
}
