package server

import (
	"embed"
	"fmt"
	"html/template"
	"net/http"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"k8s.io/klog"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	_ "git.woa.com/kateway/kateway-server/docs"
	"git.woa.com/kateway/kateway-server/pkg/mcp"
)

type Server struct {
	engine    *gin.Engine
	mcpServer *mcp.MCPServer
}

//go:embed templates/*
var f embed.FS

func New() *Server {
	// 设置 Gin 模式为 Release
	gin.SetMode(gin.ReleaseMode)
	s := &Server{
		engine:    gin.Default(),
		mcpServer: mcp.NewMCPServer(),
	}
	tmpl, err := template.New("tmpl").ParseFS(f,
		"templates/*")
	if err != nil {
		panic(err)
	}

	s.engine.GET("/healthz", s.HealthZ)

	s.engine.SetHTMLTemplate(tmpl)

	s.engine.GET("/CheckImage", s.CheckImage)

	s.engine.GET("/service/mock", s.ServiceMock)
	s.engine.Any("/service/admin/*path", s.ServiceAdmin)

	// 重新组织路由，添加主页
	s.engine.GET("/", s.HomePage)
	s.engine.GET("/cluster-console", s.ClusterConsole) // 更新路由名称保持一致性
	s.engine.GET("/scan-console", s.ClusterScanConsole)
	s.engine.GET("/clb-console", s.CLBConsole)
	s.engine.GET("/resource-console", s.ResourceConsole)
	s.engine.GET("/task-console", s.TaskConsole)
	s.engine.GET("/risk-console", s.RiskConsole)

	s.engine.GET("/inspection/cluster/task/run", s.RunClusterTask)
	s.engine.GET("/inspection/cluster/task/get", s.GetClusterTask)
	s.engine.GET("/inspection/cluster/task/update", s.UpdateClusterTask)
	s.engine.GET("/inspection/cluster/task/stop", s.StopClusterTask)

	s.engine.GET("/inspection/cluster/info/list", s.ListClusterInfo)
	s.engine.GET("/inspection/cluster/info/get", s.GetClusterInfo)
	s.engine.GET("/inspection/cluster/report/get", s.GetClusterReport)
	s.engine.GET("/inspection/clb/report/get", s.GetCLBReport)
	s.engine.GET("/inspection/resource/report/get", s.GetResourceReport)
	s.engine.GET("/inspection/risk/report/get", s.GetRiskReport)
	s.engine.GET("/inspection/task/report/get", s.GetTaskReport)
	s.engine.GET("/inspection/cluster/report/download", s.DownloadClusterReport)
	s.engine.GET("/inspection/cluster/risk/download", s.DownloadRiskReport)

	s.engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	s.engine.GET("/styles.css", func(c *gin.Context) {
		data, err := f.ReadFile("templates/styles.css")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/css", data)
	})
	s.engine.GET("/scripts.js", func(c *gin.Context) {
		data, err := f.ReadFile("templates/scripts.js")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "application/javascript", data)
	})

	s.engine.GET("/k8s-effects.js", func(c *gin.Context) {
		data, err := f.ReadFile("templates/k8s-effects.js")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "application/javascript", data)
	})
	s.engine.GET("/tech-theme.css", func(c *gin.Context) {
		data, err := f.ReadFile("templates/tech-theme.css")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/css", data)
	})
	s.engine.GET("/report-styles.css", func(c *gin.Context) {
		data, err := f.ReadFile("templates/report-styles.css")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/css", data)
	})
	s.engine.GET("/report-scripts.js", func(c *gin.Context) {
		data, err := f.ReadFile("templates/report-scripts.js")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "application/javascript", data)
	})

	// 提供头部和页脚文件
	s.engine.GET("/header.html", func(c *gin.Context) {
		data, err := f.ReadFile("templates/header.html")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/html", data)
	})

	s.engine.GET("/footer.html", func(c *gin.Context) {
		data, err := f.ReadFile("templates/footer.html")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "text/html", data)
	})

	// 提供页面加载脚本
	s.engine.GET("/page-loader.js", func(c *gin.Context) {
		data, err := f.ReadFile("templates/page-loader.js")
		if err != nil {
			c.Status(http.StatusNotFound)
			return
		}
		c.Data(http.StatusOK, "application/javascript", data)
	})

	// 添加 MCP 服务路由
	s.setupMCPRoutes()

	return s
}

func (s *Server) Run() {
	cfg := config.Get()

	// 启动 MCP 服务器（如果启用）
	if cfg.MCP.Enabled {
		go s.runMCPServer(cfg.MCP.Port)
	}

	// 启动主 HTTP 服务器
	s.engine.Run(":80")
}

// runMCPServer 在独立端口启动 MCP 服务器
func (s *Server) runMCPServer(port int) {
	klog.Infof("Starting MCP server on port %d", port)

	addr := fmt.Sprintf(":%d", port)
	if err := s.mcpServer.ServeHTTP(addr); err != nil {
		klog.Errorf("MCP server error: %v", err)
	}
}

func (s *Server) HealthZ(c *gin.Context) {
	c.Status(http.StatusOK)
}

// setupMCPRoutes 设置 MCP 服务路由
func (s *Server) setupMCPRoutes() {
	cfg := config.Get()
	if !cfg.MCP.Enabled {
		klog.Info("MCP service is disabled")
		return
	}

	klog.Infof("Setting up MCP service routes at %s", cfg.MCP.PathPrefix)

	// 创建 MCP 路由组
	mcpGroup := s.engine.Group(cfg.MCP.PathPrefix)

	// 添加 MCP HTTP 处理器
	mcpGroup.Any("/*path", s.handleMCPRequest)
}

// handleMCPRequest 处理 MCP HTTP 请求
func (s *Server) handleMCPRequest(c *gin.Context) {
	// 这里我们需要将 Gin 的 HTTP 请求转换为 MCP 服务器可以处理的格式
	// 由于 mark3labs/mcp-go 主要支持 stdio 和 WebSocket，我们需要适配 HTTP

	// 暂时返回 MCP 服务信息
	c.JSON(http.StatusOK, gin.H{
		"service": "Kateway MCP Server",
		"version": "1.0.0",
		"status":  "running",
		"tools": []string{
			"list_cluster_info",
			"get_cluster_info",
			"get_cluster_report",
			"get_cluster_risk_report",
			"get_clb_report",
			"get_resource_report",
			"get_task_report",
			"get_risk_report",
			"download_cluster_report",
			"download_risk_report",
		},
		"message": "MCP service is running. Use MCP client to connect via stdio or WebSocket.",
	})
}
